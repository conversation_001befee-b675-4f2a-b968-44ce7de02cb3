{"name": "login-verification-client", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "vite build"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.1.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^10.12.16", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.44.3", "tailwind-merge": "^3.3.1", "zod": "^3.21.4"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.2", "typescript": "^5.0.2", "vite": "^5.0.10"}}