{"name": "object-assign", "version": "4.1.1", "description": "ES2015 `Object.assign()` ponyfill", "license": "MIT", "repository": "sindresorhus/object-assign", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["object", "assign", "extend", "properties", "es2015", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "devDependencies": {"ava": "^0.16.0", "lodash": "^4.16.4", "matcha": "^0.7.0", "xo": "^0.16.0"}}